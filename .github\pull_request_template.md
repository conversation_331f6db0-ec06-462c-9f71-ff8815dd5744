## 📋 Pull Request 描述

### 🎯 变更类型
请选择适用的变更类型：

- [ ] 🐛 Bug修复 (非破坏性变更，修复了一个问题)
- [ ] ✨ 新功能 (非破坏性变更，添加了新功能)
- [ ] 💥 破坏性变更 (修复或功能会导致现有功能无法正常工作)
- [ ] 📚 文档更新 (仅文档变更)
- [ ] 🎨 样式调整 (格式化、缺少分号等，不改变代码逻辑)
- [ ] ♻️ 代码重构 (既不修复bug也不添加功能的代码变更)
- [ ] ⚡ 性能优化 (提高性能的代码变更)
- [ ] 🧪 测试 (添加缺失的测试或修正现有测试)
- [ ] 🔧 构建/工具 (影响构建系统或外部依赖的变更)

### 📝 变更摘要
简洁地描述这个PR的主要变更：

### 🔗 相关Issue
关闭的Issue: #(issue编号)

### 📊 变更详情

#### 🔧 技术变更
- [ ] 前端变更
- [ ] 后端变更
- [ ] 数据库变更
- [ ] API变更
- [ ] 配置变更
- [ ] 依赖更新

#### 📱 影响范围
- [ ] 用户界面
- [ ] 用户体验
- [ ] 性能
- [ ] 安全性
- [ ] 兼容性
- [ ] 可访问性

### 🧪 测试

#### 测试类型
- [ ] 单元测试
- [ ] 集成测试
- [ ] 端到端测试
- [ ] 手动测试
- [ ] 性能测试
- [ ] 安全测试

#### 测试覆盖
- [ ] 新增代码已添加测试
- [ ] 现有测试仍然通过
- [ ] 测试覆盖率没有降低

#### 测试步骤
描述如何测试这些变更：

1. 
2. 
3. 

### 📸 截图/演示

如果有UI变更，请提供截图或GIF演示：

**变更前:**
<!-- 添加截图 -->

**变更后:**
<!-- 添加截图 -->

### 🔍 代码审查要点

请审查者特别关注以下方面：

- [ ] 代码逻辑正确性
- [ ] 性能影响
- [ ] 安全考虑
- [ ] 错误处理
- [ ] 代码风格
- [ ] 文档完整性

### 📚 文档更新

- [ ] README.md 已更新
- [ ] API文档已更新
- [ ] 用户文档已更新
- [ ] 开发文档已更新
- [ ] 变更日志已更新
- [ ] 不需要文档更新

### 🚀 部署注意事项

- [ ] 需要数据库迁移
- [ ] 需要环境变量更新
- [ ] 需要依赖包更新
- [ ] 需要配置文件更新
- [ ] 需要重启服务
- [ ] 无特殊部署要求

### ⚠️ 破坏性变更

如果这是破坏性变更，请描述：

- 什么被改变了？
- 为什么需要这个变更？
- 如何迁移现有代码？

### 📋 检查清单

#### 代码质量
- [ ] 代码遵循项目编码规范
- [ ] 代码已经过自我审查
- [ ] 代码注释清晰，特别是复杂逻辑部分
- [ ] 没有调试代码或注释掉的代码

#### 测试
- [ ] 所有新增和修改的代码都有相应的测试
- [ ] 所有测试都通过
- [ ] 测试覆盖了边界情况和错误情况

#### 文档
- [ ] 相关文档已更新
- [ ] API变更已在文档中体现
- [ ] 新功能有使用说明

#### 性能和安全
- [ ] 变更不会显著影响性能
- [ ] 没有引入安全漏洞
- [ ] 敏感信息没有硬编码

#### 兼容性
- [ ] 变更向后兼容
- [ ] 在不同浏览器中测试通过
- [ ] 移动端适配正常

### 🎯 审查者指南

#### 重点审查项目
1. **功能正确性**: 代码是否实现了预期功能
2. **代码质量**: 代码是否清晰、可维护
3. **性能影响**: 是否有性能问题
4. **安全考虑**: 是否存在安全风险
5. **测试完整性**: 测试是否充分

#### 审查建议
- 请仔细检查核心逻辑
- 关注错误处理和边界情况
- 验证用户体验是否良好
- 确认文档是否准确

### 📞 联系信息

如果审查者有问题，可以通过以下方式联系：

- GitHub: @your-username
- 邮箱: <EMAIL>
- 其他: 

### 🙏 致谢

感谢以下人员的帮助和建议：

- @contributor1
- @contributor2

---

### 📝 额外说明

<!-- 添加任何其他相关信息 -->

---

**感谢你的贡献！请确保所有检查项都已完成。** ✨
