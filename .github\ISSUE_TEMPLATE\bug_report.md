---
name: 🐛 Bug Report
about: 报告一个bug来帮助我们改进
title: '[BUG] '
labels: ['bug', 'needs-triage']
assignees: ''
---

## 🐛 Bug 描述

简洁清晰地描述这个bug是什么。

## 🔄 复现步骤

详细描述如何复现这个问题：

1. 进入 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## 🎯 期望行为

清晰简洁地描述你期望发生什么。

## 📸 截图

如果适用，添加截图来帮助解释你的问题。

## 💻 环境信息

**桌面环境 (请完成以下信息):**
- OS: [e.g. Windows 10, macOS 12.0, Ubuntu 20.04]
- Browser: [e.g. Chrome 95, Firefox 94, Safari 15]
- Version: [e.g. 1.0.0]

**移动设备 (请完成以下信息):**
- Device: [e.g. iPhone 13, Samsung Galaxy S21]
- OS: [e.g. iOS 15.0, Android 12]
- Browser: [e.g. Safari, Chrome Mobile]
- Version: [e.g. 1.0.0]

**技术环境:**
- Node.js: [e.g. 18.0.0]
- Python: [e.g. 3.9.7]
- npm: [e.g. 8.0.0]

## 📋 错误日志

如果有相关的错误日志，请粘贴在这里：

```
粘贴错误日志
```

## 🔍 浏览器控制台

如果是前端问题，请提供浏览器控制台的错误信息：

```javascript
// 粘贴控制台错误信息
```

## 🌐 网络信息

如果是网络相关问题：
- [ ] 网络连接正常
- [ ] 可以访问其他网站
- [ ] 使用了代理或VPN
- [ ] 在公司网络环境下

## 📊 影响程度

- [ ] 🔴 严重 - 系统无法使用
- [ ] 🟡 中等 - 功能受限但可以使用
- [ ] 🟢 轻微 - 小问题，不影响主要功能

## 🔄 出现频率

- [ ] 总是出现
- [ ] 经常出现
- [ ] 偶尔出现
- [ ] 只出现一次

## 🛠️ 尝试的解决方案

描述你已经尝试过的解决方案：

- [ ] 刷新页面
- [ ] 清除浏览器缓存
- [ ] 重启应用
- [ ] 检查网络连接
- [ ] 其他: ___________

## 📝 附加信息

添加任何其他关于问题的信息，比如：
- 问题开始出现的时间
- 是否在特定条件下才出现
- 相关的用户操作
- 其他可能有用的信息

## ✅ 检查清单

在提交之前，请确认：

- [ ] 我已经搜索了现有的issues，确认这个问题没有被报告过
- [ ] 我已经提供了足够的信息来复现这个问题
- [ ] 我已经尝试了基本的故障排除步骤
- [ ] 我使用的是最新版本的系统

---

**感谢你的bug报告！我们会尽快处理这个问题。** 🙏
